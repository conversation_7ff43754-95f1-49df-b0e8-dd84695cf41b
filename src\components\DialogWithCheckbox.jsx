import debounce from "lodash/debounce";
import React, { useEffect, useState } from "react";
import { Controller, useForm, useWatch } from "react-hook-form";
import { Dimensions, ScrollView, StyleSheet, View } from "react-native";
import {
    Button,
    Checkbox,
    Dialog,
    Portal,
    Text,
    TextInput,
    useTheme
} from "react-native-paper";
import { isFunction } from "../utils";


/**
 * [ {id, name} ] -> { id: {id, name} }
 * @param {[{id, name}]} checkedArray
 */
const checkedArrayToObject = (checkedArray) => {
    const obj = {};
    checkedArray?.map((item) => {
        obj[item.id] = { ...item };
    });
    return obj;
};

/**
 * { id: {id, name} } -> [ {id, name} ]
 * @param {*} checkedObject
 */
const checkedObjectToArray = (checkedObject) => {
    const arr = [];
    for (const [_key, value] of Object.entries(checkedObject)) {
        value ? arr.push(value) : undefined;
    }
    return arr;
};

/*
dialogItemArray = [{id: 1, name: "Option1"}]
*/
export const DialogWithCheckbox = ({ title, visible, onOK, onCancel, okBtnLabel = "确定", cancelBtnLabel = "取消", dialogState, setDialogState, dialogItemArray, nullOptionsTip, ...props }) => {
    const [checked, setChecked] = useState(checkedArrayToObject(dialogState)); // checked should be: { 1: {"id": 1, "name": "DEPT-01"} }, where the key equals the id of the val
    const [filteredArray, setFilteredArray] = useState(dialogItemArray);
    const theme = useTheme();

    const {
        control,
    } = useForm({
        defaultValues: {
            filterWith: "",
        },
    });

    const filterWith = useWatch({ control, name: "filterWith" });

    const updateItemArray = debounce((filterWith) => {
        setFilteredArray(filterWith ? dialogItemArray.filter(item => item.name.includes(filterWith)) : dialogItemArray);
    }, 100);

    useEffect(() => {
        updateItemArray(filterWith);
    }, [filterWith, dialogItemArray]);

    useEffect(() => {
        setChecked(checkedArrayToObject(dialogState));
    }, [dialogState]);

    return (
        <Portal>
            <Dialog
                onDismiss={() => { setChecked(checkedArrayToObject(dialogState)); onCancel();}}
                visible={visible}
                style={{ maxHeight: 0.8 * Dimensions.get("window").height }}
            >
                <Dialog.Title>{title}</Dialog.Title>
                <View style={styles.searchBarContainer}>
                    <Controller
                        control={control}
                        //rules={{ required: true, }}
                        render={({ field: { onChange, onBlur, value } }) => (
                            <TextInput
                                label=""
                                mode="outlined"
                                onBlur={onBlur}
                                onChangeText={onChange}
                                value={value}
                                right={<TextInput.Icon icon="magnify" />}
                                style={{
                                    flex: 1,
                                    marginHorizontal: "5%",
                                    backgroundColor: theme.colors.surface,
                                }}
                            />
                        )}
                        name="filterWith"
                    />
                </View>

                <Dialog.ScrollArea style={styles.container}>
                    {!dialogItemArray || dialogItemArray.length === 0 ?
                        <Text variant="titleMedium" style={{ textAlign: "center", }}>{isFunction(nullOptionsTip) ? nullOptionsTip() : nullOptionsTip || "发生未知错误, 请联系管理员!"}</Text>
                        :
                        <ScrollView>
                            <View>
                                {filteredArray?.map((item, index) => {
                                    return (
                                        <Checkbox.Item
                                            key={index}
                                            label={item.name}
                                            status={checked[item.id] ? "checked" : "unchecked"}
                                            onPress={() => {
                                                const cur = {};
                                                checked[item.id] ? cur[item.id] = undefined : cur[item.id] = item;
                                                setChecked({ ...checked, ...cur });
                                            }}
                                        />
                                    );
                                })}
                            </View>
                        </ScrollView>}
                </Dialog.ScrollArea>

                <Dialog.Actions>
                        <Button
                            onPress={() => {
                                const allSelected = {};
                                filteredArray?.forEach(item => {
                                    allSelected[item.id] = item;
                                });
                                setChecked({ ...checked, ...allSelected });
                            }}
                        >
                            全选
                        </Button>
                        <Button
                            onPress={() => {
                                const allUnselected = {};
                                filteredArray?.forEach(item => {
                                    allUnselected[item.id] = undefined;
                                });
                                setChecked({ ...checked, ...allUnselected });
                            }}
                        >
                            全不选
                        </Button>

                    <Button onPress={() => {
                        setChecked(checkedArrayToObject(dialogState));
                        onCancel();
                    }}>
                        {cancelBtnLabel}
                    </Button>
                    <Button onPress={() => {
                        const checkedRes = checkedObjectToArray(checked);
                        setDialogState(checkedRes);
                        onOK(checkedRes);
                    }}>
                        {okBtnLabel}
                    </Button>
                </Dialog.Actions>
            </Dialog>
        </Portal>
    );
};

const styles = StyleSheet.create({
    container: {
        //maxHeight: 270,
        paddingHorizontal: 0,
    },
    row: { // 没使用
        flexDirection: "row",
        alignItems: "center",
        paddingHorizontal: 16,
        paddingVertical: 8,
    },
    text: {
        paddingLeft: 8,
    },
    searchBarContainer: {
        flexDirection: "row",
        justifyContent: "center",
        alignItems: "center",
        width: "100%",
        //paddingVertical: 8,
    },
    selectAllContainer: {
        flexDirection: "row",
        justifyContent: "flex-start",
        alignItems: "left",
    },
    selectButton: {
        minWidth: 80,
    },
});
