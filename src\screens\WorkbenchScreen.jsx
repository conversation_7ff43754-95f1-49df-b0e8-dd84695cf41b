import React from "react";
//import { StyleSheet } from "react-native";
import { Divider, List } from "react-native-paper";
import { CardWorkbench } from "../components/CardWorkbench";
import { PermissionCode, roleVerify, UserTypeList, verifySomeUserTypes } from "../utils/permits";
import ScreenWrapper from "./ScreenWrapper";


// Divider: https://callstack.github.io/react-native-paper/docs/components/Divider
/**
 * 注意, 菜单的显示需要增加测试用户, 暂未实现, 可以通过掩码在userType中标注.
 * @param {object} arg
 * @returns
 */
const WorkbenchScreen = ({ navigation }) => {
    //console.log("navigation ListWorbench", navigation);
    return (
        <ScreenWrapper>
            <Divider bold={true} />

            {(verifySomeUserTypes(UserTypeList.orgAdmin) || roleVerify(PermissionCode.projStart)) && <>
                <List.Section title={"项目管理"}>
                    <CardWorkbench title="水平衡"   desc="创建/查看/修改项目" naviTo="WorkbenchWaterBalanceListing" icon="water-outline" navigation={navigation} />
                    <CardWorkbench title="零碳诊断" desc="创建/查看/修改项目" naviTo="WorkbenchZeroCarbonListing"   icon="molecule-co2"  navigation={navigation} />

                    <CardWorkbench title="项目拷贝" desc="根据已有项目创建副本" naviTo="ProjectCopying"        icon="forum-plus-outline"  navigation={navigation} />

                    {/*<CardWorkbench title="电平衡" desc="发起电平衡项目" naviTo="TestScreen2" icon="lightning-bolt-outline" navigation={navigation} />*/}

                </List.Section>
                <Divider bold={true} /></>}

            {verifySomeUserTypes(UserTypeList.sysAdmin, UserTypeList.subAdmin) && <>
                <List.Section title={"App订阅管理"}>
                    <CardWorkbench title="管理App使用机构" naviTo="WorkbenchOrgListing" icon="account-multiple-plus-outline" navigation={navigation} />
                </List.Section>
                <Divider bold={true} /></>}

            {(verifySomeUserTypes(UserTypeList.orgAdmin) || roleVerify(PermissionCode.userMgt)) && <>
                <List.Section title={"成员管理"}>
                    <CardWorkbench title="管理项目成员" naviTo="WorkbenchUserListing" icon="account-plus-outline" navigation={navigation} />
                </List.Section>
                <Divider bold={true} /></>}

            {(verifySomeUserTypes(UserTypeList.orgAdmin) || roleVerify(PermissionCode.orgCfg)) && <>
                <List.Section title={"组织架构管理"}>
                    <CardWorkbench title="部门管理" naviTo="WorkbenchDepartmentsListing" icon="account-supervisor-outline" navigation={ navigation } />
                    <CardWorkbench title="岗位管理" naviTo="WorkbenchPositionsListing" icon="briefcase-outline" navigation={navigation} />
                    <CardWorkbench title="角色权限管理" naviTo="WorkbenchRolesListing" icon="account-key-outline" navigation={navigation} />
                </List.Section>
                <Divider bold={true} /></>}

            {(verifySomeUserTypes(UserTypeList.sysAdmin, UserTypeList.qa_0) || (false && (verifySomeUserTypes(UserTypeList.orgAdmin) || roleVerify(PermissionCode.clientMgt)))) && <>
                <List.Section title={"客户管理"}>
                    <CardWorkbench title="客户管理" naviTo="WorkbenchClientsListing" icon="account-circle-outline" navigation={navigation} />
                </List.Section>
                <Divider bold={true} /></>}

            {(verifySomeUserTypes(UserTypeList.sysAdmin, UserTypeList.qa_0) || (false && (verifySomeUserTypes(UserTypeList.orgAdmin) || roleVerify(PermissionCode.projCfg)))) && <>
                <List.Section title={"项目配置"}>
                    <CardWorkbench title="项目库管理" naviTo="WorkbenchProjectBaseListing" icon="account-wrench-outline" navigation={navigation} />
                    {/*<CardWorkbench title="项目类型管理" naviTo="WorkbenchUserListing" icon="account-wrench-outline" navigation={navigation} />*/}
                </List.Section>
                <Divider bold={true} /></>}

            {verifySomeUserTypes(UserTypeList.sysAdmin, UserTypeList.qa_0)  && <>
                <List.Section title={"功能测试"}>
                    <CardWorkbench title="Skia测试"             naviTo="SketchingTest" icon="ab-testing" navigation={navigation} />
                    <CardWorkbench title="测试新输入组件"             naviTo="TestInputTypes" icon="ab-testing" navigation={navigation} />
                    <CardWorkbench title="测试ReactHookForm+Zustand" naviTo="TestReactHookFormZustand" icon="ab-testing" navigation={navigation} />
                    <CardWorkbench title="测试ReactHookForm+MMKV"    naviTo="TestReactHookFormMMKV" icon="ab-testing" navigation={navigation} />
                    <CardWorkbench title="测试文件上传"              naviTo="TestUploadFiles" icon="ab-testing" navigation={navigation} />
                    <CardWorkbench title="测试Bottom Sheet"         naviTo="TestBottomSheet" icon="ab-testing" navigation={navigation} />
                    <CardWorkbench title="测试图片输入"              naviTo="TestImageInput" icon="ab-testing" navigation={navigation} />
                </List.Section>
                <Divider bold={true} /></>}

        </ScreenWrapper>
    );
};

/*
const styles = StyleSheet.create({
    container: {
        flex: 1,
        marginTop: 4,
        marginBottom: 4,
        marginHorizontal: 10,
        //marginVertical: 10,
    },
    card: {
        borderWidth: 1,
        marginVertical: 2,
    }
});
*/

export default WorkbenchScreen;
