{"name": "LowCarbonManagement", "version": "2.3.2", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint --ext .jsx --ext .js src/", "start": "react-native start", "test": "jest"}, "dependencies": {"@gorhom/bottom-sheet": "^5.1.6", "@hookform/error-message": "^2.0.1", "@hookform/resolvers": "^5.1.1", "@react-native-camera-roll/camera-roll": "^7.10.1", "@react-native-community/datetimepicker": "^8.4.2", "@react-native-community/geolocation": "^3.4.0", "@react-native-community/netinfo": "^11.4.1", "@react-native-community/slider": "^4.5.7", "@react-native-vector-icons/material-design-icons": "^12.0.1", "@react-navigation/bottom-tabs": "^7.4.2", "@react-navigation/drawer": "^7.5.2", "@react-navigation/native": "^7.1.14", "@react-navigation/stack": "^7.4.2", "@shopify/react-native-skia": "^2.0.7", "@tanstack/react-query": "^5.81.5", "dayjs": "^1.11.13", "fast-text-encoding": "^1.0.6", "i18next": "^25.2.1", "joi": "^17.13.3", "ky": "^1.8.1", "lodash": "^4.17.21", "prop-types": "^15.8.1", "react": "19.1.0", "react-hook-form": "^7.59.0", "react-i18next": "^15.5.3", "react-native": "0.80.0", "react-native-blob-util": "^0.22.2", "react-native-compressor": "^1.12.0", "react-native-device-info": "^14.0.4", "react-native-fs": "^2.20.0", "react-native-gesture-handler": "^2.26.0", "react-native-image-crop-picker": "^0.50.1", "react-native-mime-types": "^2.5.0", "react-native-mmkv": "^3.3.0", "react-native-paper": "^5.14.5", "react-native-permissions": "^5.4.1", "react-native-reanimated": "^3.18.0", "react-native-safe-area-context": "^5.5.0", "react-native-screens": "^4.11.1", "react-native-status-bar-height": "^2.6.0", "react-native-view-shot": "^4.0.3", "zustand": "^5.0.6"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/preset-env": "^7.25.3", "@babel/runtime": "^7.25.0", "@react-native-community/cli": "19.0.0", "@react-native-community/cli-platform-android": "19.0.0", "@react-native-community/cli-platform-ios": "19.0.0", "@react-native/babel-preset": "0.80.0", "@react-native/eslint-config": "0.80.0", "@react-native/metro-config": "0.80.0", "@react-native/typescript-config": "0.80.0", "@types/jest": "^29.5.14", "@types/react": "^19.1.0", "@types/react-test-renderer": "^19.1.0", "eslint": "^8.57.1", "eslint-config-prettier": "^10.1.5", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "jest": "^29.7.0", "metro-react-native-babel-preset": "^0.77.0", "prettier": "3.6.2", "react-test-renderer": "19.1.0", "typescript": "5.3.3"}, "engines": {"node": ">=18"}}